#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Minecraft服务器BAT文件编辑器
支持图形界面配置所有启动参数并保存到本地
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import json
import re
import subprocess
import platform
from pathlib import Path

# 尝试导入拖放支持
try:
    from tkinterdnd2 import DND_FILES, TkinterDnD
    HAS_DND = True
except ImportError:
    HAS_DND = False

class MinecraftBatEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("🎮 Minecraft服务器BAT文件编辑器")
        self.root.geometry("1700x950")  # 增加宽度以确保右侧文字完整显示

        # 设置窗口图标和样式
        self.setup_window_style()

        # 配置文件路径
        self.config_file = "minecraft_config.json"

        # Minecraft版本信息
        self.minecraft_versions = {
            "1.20+": {
                "name": "1.20及以上版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200",
                "min_java": "Java 17+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "1.17-1.19": {
                "name": "1.17-1.19版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled",
                "min_java": "Java 16+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "1.13-1.16": {
                "name": "1.13-1.16版本",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "1.7-1.12": {
                "name": "1.7-1.12版本 (首次引入nogui)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "gui"
            },
            "1.6及以下": {
                "name": "1.6及以下版本 (仅GUI模式)",
                "supports_nogui": False,
                "supports_gui": True,
                "default_jvm_args": "",
                "min_java": "Java 6+",
                "port_param": "-p",
                "recommended_gui": "gui"
            },
            "forge": {
                "name": "Forge服务器 (模组)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -Dfml.queryResult=confirm",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "gui"
            },
            "fabric": {
                "name": "Fabric服务器 (轻量模组)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "paper": {
                "name": "Paper服务器 (高性能)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC -XX:+ParallelRefProcEnabled -XX:MaxGCPauseMillis=200 -XX:+UnlockExperimentalVMOptions -XX:+DisableExplicitGC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            },
            "spigot": {
                "name": "Spigot服务器 (插件)",
                "supports_nogui": True,
                "supports_gui": True,
                "default_jvm_args": "-XX:+UseG1GC",
                "min_java": "Java 8+",
                "port_param": "--port",
                "recommended_gui": "nogui"
            }
        }

        # JVM优化参数配置
        self.jvm_options = {
            "use_g1gc": {"name": "使用G1垃圾收集器", "param": "-XX:+UseG1GC", "default": True, "category": "基础"},
            "parallel_ref": {"name": "并行引用处理", "param": "-XX:+ParallelRefProcEnabled", "default": True, "category": "基础"},
            "max_gc_pause": {"name": "最大GC暂停时间", "param": "-XX:MaxGCPauseMillis=", "default": "200", "category": "基础", "unit": "毫秒"},
            "unlock_experimental": {"name": "解锁实验性选项", "param": "-XX:+UnlockExperimentalVMOptions", "default": True, "category": "基础"},
            "disable_explicit_gc": {"name": "禁用显式GC", "param": "-XX:+DisableExplicitGC", "default": True, "category": "基础"},
            "always_pre_touch": {"name": "预分配内存", "param": "-XX:+AlwaysPreTouch", "default": True, "category": "高级"},
            "heap_waste_percent": {"name": "堆浪费百分比", "param": "-XX:G1HeapWastePercent=", "default": "5", "category": "高级", "unit": "%"},
            "mixed_gc_count": {"name": "混合GC目标次数", "param": "-XX:G1MixedGCCountTarget=", "default": "4", "category": "高级"},
            "heap_occupancy": {"name": "堆占用触发百分比", "param": "-XX:InitiatingHeapOccupancyPercent=", "default": "15", "category": "高级", "unit": "%"},
            "mixed_gc_threshold": {"name": "混合GC存活阈值", "param": "-XX:G1MixedGCLiveThresholdPercent=", "default": "90", "category": "高级", "unit": "%"},
            "rset_updating_pause": {"name": "RSet更新暂停时间", "param": "-XX:G1RSetUpdatingPauseTimePercent=", "default": "5", "category": "高级", "unit": "%"},
            "survivor_ratio": {"name": "幸存者区比例", "param": "-XX:SurvivorRatio=", "default": "32", "category": "高级"},
            "perf_disable_shared": {"name": "禁用共享内存性能", "param": "-XX:+PerfDisableSharedMem", "default": True, "category": "高级"},
            "max_tenuring": {"name": "最大晋升阈值", "param": "-XX:MaxTenuringThreshold=", "default": "1", "category": "高级"},
            "g1_new_size": {"name": "G1新生代最小比例", "param": "-XX:G1NewSizePercent=", "default": "30", "category": "高级", "unit": "%"},
            "g1_max_new_size": {"name": "G1新生代最大比例", "param": "-XX:G1MaxNewSizePercent=", "default": "40", "category": "高级", "unit": "%"},
            "g1_heap_region": {"name": "G1堆区域大小", "param": "-XX:G1HeapRegionSize=", "default": "8M", "category": "高级"},
            "g1_reserve": {"name": "G1保留百分比", "param": "-XX:G1ReservePercent=", "default": "20", "category": "高级", "unit": "%"},
            "aikars_flags": {"name": "Aikar's Flags标识", "param": "-Dusing.aikars.flags=https://mcflags.emc.gs", "default": True, "category": "标识"},
            "new_flags": {"name": "新版Flags标识", "param": "-Daikars.new.flags=true", "default": True, "category": "标识"}
        }

        # 默认配置
        self.default_config = {
            "java_path": "java",
            "jar_file": "server.jar",
            "minecraft_version": "1.20+",
            "min_memory": "1G",
            "max_memory": "4G",

            "gui_mode": "--nogui",
            "additional_jvm_args": "",
            "additional_args": "",
            "pause_on_exit": True,
            "output_file": "start_server.bat",
            "auto_detect_version": True,
            "show_startup_info": True,
            "show_shutdown_info": True,
            "custom_startup_message": "",
            "custom_shutdown_message": "",
            "bat_title": "Minecraft Server",
            "use_advanced_jvm": False,
            "use_custom_args": False,
            "file_encoding": "utf-8",
            "auto_restart": False,
            "use_aikar_flags": False,
            "preview_editable": True,
            "use_chcp": True,
            "use_file_encoding": True
        }

        self.config = self.load_config()
        self.create_widgets()

    def setup_window_style(self):
        """设置窗口样式"""
        # 设置窗口背景色
        self.root.configure(bg='#f0f0f0')

        # 设置窗口初始尺寸和位置
        width = 1500
        height = 850
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        # 计算居中位置，并向上移动100像素
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2) - 100  # 向上移动100像素

        # 设置窗口几何
        self.root.geometry(f"{width}x{height}+{x}+{y}")

        # 设置窗口最小尺寸
        self.root.minsize(1500, 850)

        # 设置窗口最大尺寸（防止过大）
        max_width = min(1800, int(screen_width * 0.9))
        max_height = min(1000, int(screen_height * 0.9))
        self.root.maxsize(max_width, max_height)

        # 配置ttk样式
        style = ttk.Style()

        # 设置主题
        try:
            style.theme_use('clam')  # 使用clam主题，比较现代
        except:
            pass

        # 自定义样式 - 增大字体
        style.configure('Title.TLabel',
                       font=('Microsoft YaHei', 20, 'bold'),  # 增大标题字体
                       foreground='#2c3e50',
                       background='#f0f0f0')

        style.configure('Heading.TLabel',
                       font=('Microsoft YaHei', 14, 'bold'),  # 增大标题字体
                       foreground='#34495e',
                       background='#f0f0f0')

        # 设置默认字体大小
        style.configure('TLabel', font=('Microsoft YaHei', 11))  # 增大标签字体
        style.configure('TEntry', font=('Microsoft YaHei', 11))  # 增大输入框字体
        style.configure('TCombobox', font=('Microsoft YaHei', 11))  # 增大下拉框字体
        style.configure('TCheckbutton', font=('Microsoft YaHei', 11))  # 增大复选框字体
        style.configure('TRadiobutton', font=('Microsoft YaHei', 11))  # 增大单选框字体
        style.configure('TLabelframe.Label', font=('Microsoft YaHei', 12, 'bold'))  # 增大框架标题字体

        style.configure('Custom.TButton',
                       font=('Microsoft YaHei', 11),  # 增大按钮字体
                       padding=(12, 8))  # 增大按钮内边距

        # 设置预览框架样式
        style.configure('Preview.TLabelframe', background='#f0f0f0')
        style.configure('Preview.TLabelframe.Label', background='#f0f0f0')

        style.map('Custom.TButton',
                 background=[('active', '#3498db'),
                           ('pressed', '#2980b9')])

    def center_window(self):
        """居中显示窗口"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2) - 50  # 向上移动50像素
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)  # 左侧配置区域
        main_frame.columnconfigure(1, weight=0)  # 左侧滚动条
        main_frame.columnconfigure(2, weight=2)  # 中间预览区域
        main_frame.columnconfigure(3, weight=1)  # 右侧配置区域
        main_frame.columnconfigure(4, weight=0)  # 右侧滚动条
        main_frame.rowconfigure(1, weight=1)     # 让内容区域可以扩展

        row = 0

        # 创建标题区域（跨越五列）
        title_frame = tk.Frame(main_frame, bg='#3498db', height=45)  # 进一步减少标题框高度
        title_frame.grid(row=row, column=0, columnspan=5, sticky=(tk.W, tk.E), pady=(0, 2))  # 进一步减少底部间距
        title_frame.grid_propagate(False)
        title_frame.columnconfigure(0, weight=1)

        # 标题
        title_label = tk.Label(title_frame, text="🎮 Minecraft服务器启动配置",
                              font=("Microsoft YaHei", 20, "bold"),  # 进一步减小标题字体
                              fg='white', bg='#3498db')
        title_label.grid(row=0, column=0, pady=(3, 0))  # 进一步减少标题内部间距

        # 副标题
        subtitle_label = tk.Label(title_frame, text="简单易用的BAT文件生成工具",
                                 font=("Microsoft YaHei", 11),  # 进一步减小副标题字体
                                 fg='#ecf0f1', bg='#3498db')
        subtitle_label.grid(row=1, column=0, pady=(0, 3))  # 进一步减少副标题间距

        row += 1

        # 创建三栏布局：左侧配置、中间预览、右侧配置
        # 配置主框架的列权重，实现自动调整（无滚动条）
        main_frame.columnconfigure(0, weight=1, minsize=380)  # 左侧列
        main_frame.columnconfigure(1, weight=2, minsize=500)  # 中间预览列，更大
        main_frame.columnconfigure(2, weight=1, minsize=480)  # 右侧列，增加宽度以显示完整文字

        # 配置主框架的行权重，确保垂直方向也能自动调整
        main_frame.rowconfigure(0, weight=1)

        # 左侧配置区域（无滚动）
        left_frame = ttk.Frame(main_frame)
        left_frame.grid(row=row, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        left_frame.columnconfigure(1, weight=1)

        # 中间预览区域
        middle_frame = ttk.Frame(main_frame)
        middle_frame.grid(row=row, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        middle_frame.columnconfigure(0, weight=1)
        middle_frame.rowconfigure(0, weight=1)

        # 右侧配置区域（无滚动）
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=row, column=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        right_frame.columnconfigure(1, weight=1)

        # 重置行计数器
        left_row = 0
        right_row = 0

        # Java路径
        java_frame = ttk.Frame(left_frame)
        java_frame.grid(row=left_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        java_frame.columnconfigure(1, weight=1)

        ttk.Label(java_frame, text="Java路径:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.java_path_var = tk.StringVar(value=self.config["java_path"])
        self.java_entry = ttk.Entry(java_frame, textvariable=self.java_path_var)
        self.java_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))

        # 添加Java路径拖放提示
        java_tip_label = ttk.Label(java_frame, text="💡 可拖放Java可执行文件到输入框",
                                  foreground="gray", font=("Microsoft YaHei", 8))
        java_tip_label.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(2, 0))

        # Java选择按钮
        java_button_frame = ttk.Frame(java_frame)
        java_button_frame.grid(row=0, column=2, pady=5, padx=(5, 0))

        ttk.Button(java_button_frame, text="浏览",
                  command=self.browse_java).pack(side=tk.LEFT, padx=(0, 2))
        ttk.Button(java_button_frame, text="搜索",
                  command=self.search_java).pack(side=tk.LEFT)

        # Java版本选择下拉框
        ttk.Label(java_frame, text="检测到的Java版本:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.java_versions_var = tk.StringVar()
        self.java_combo = ttk.Combobox(java_frame, textvariable=self.java_versions_var,
                                     state="readonly", width=40)
        self.java_combo.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        self.java_combo.bind('<<ComboboxSelected>>', self.on_java_selected)

        # 添加拖放提示和功能
        self.setup_drag_drop(self.java_entry, "java")
        self.setup_windows_drag_drop(self.java_entry, "java")

        # 初始搜索Java版本
        self.search_java_versions()
        left_row += 1

        # JAR文件
        jar_frame = ttk.Frame(left_frame)
        jar_frame.grid(row=left_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        jar_frame.columnconfigure(1, weight=1)

        ttk.Label(jar_frame, text="服务器JAR文件:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.jar_file_var = tk.StringVar(value=self.config["jar_file"])
        self.jar_entry = ttk.Entry(jar_frame, textvariable=self.jar_file_var)
        self.jar_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        ttk.Button(jar_frame, text="浏览",
                  command=self.browse_jar).grid(row=0, column=2, pady=2, padx=(5, 0))

        # 添加JAR文件拖放提示
        jar_tip_label = ttk.Label(jar_frame, text="💡 可拖放JAR文件到输入框",
                                 foreground="gray", font=("Microsoft YaHei", 8))
        jar_tip_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(2, 0))

        # 添加拖放提示和功能
        self.setup_drag_drop(self.jar_entry, "jar")
        self.setup_windows_drag_drop(self.jar_entry, "jar")
        left_row += 1

        # 版本选择框架
        version_frame = ttk.LabelFrame(left_frame, text="🎯 Minecraft版本设置", padding="3")
        version_frame.grid(row=left_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        version_frame.columnconfigure(1, weight=1)

        ttk.Label(version_frame, text="服务器类型:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.version_var = tk.StringVar(value=self.config["minecraft_version"])
        version_combo = ttk.Combobox(version_frame, textvariable=self.version_var,
                                   values=list(self.minecraft_versions.keys()),
                                   state="readonly", width=20)
        version_combo.grid(row=0, column=1, sticky=tk.W, pady=2, padx=(5, 0))
        version_combo.bind('<<ComboboxSelected>>', self.on_version_change)

        self.auto_detect_var = tk.BooleanVar(value=self.config.get("auto_detect_version", True))
        ttk.Checkbutton(version_frame, text="自动检测版本 (基于JAR文件名)",
                       variable=self.auto_detect_var,
                       command=self.on_auto_detect_change).grid(row=1, column=0, columnspan=2,
                                                               sticky=tk.W, pady=5)

        # 版本信息显示
        self.version_info_var = tk.StringVar()
        self.version_info_label = ttk.Label(version_frame, textvariable=self.version_info_var,
                                          foreground="blue", font=("Arial", 9))
        self.version_info_label.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=5)

        left_row += 1

        # 内存设置框架
        memory_frame = ttk.LabelFrame(left_frame, text="💾 内存设置", padding="3")
        memory_frame.grid(row=left_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        memory_frame.columnconfigure(1, weight=1)
        memory_frame.columnconfigure(3, weight=1)

        ttk.Label(memory_frame, text="最小内存:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.min_memory_var = tk.StringVar(value=self.config["min_memory"])
        ttk.Entry(memory_frame, textvariable=self.min_memory_var, width=8).grid(
            row=0, column=1, sticky=tk.W, pady=2, padx=(5, 10))

        ttk.Label(memory_frame, text="最大内存:").grid(row=0, column=2, sticky=tk.W, pady=2)
        self.max_memory_var = tk.StringVar(value=self.config["max_memory"])
        ttk.Entry(memory_frame, textvariable=self.max_memory_var, width=8).grid(
            row=0, column=3, sticky=tk.W, pady=2, padx=(5, 0))

        left_row += 1

        # 服务器设置框架
        server_frame = ttk.LabelFrame(left_frame, text="⚙️ 服务器设置", padding="3")
        server_frame.grid(row=left_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        server_frame.columnconfigure(1, weight=1)

        # BAT文件标题设置
        ttk.Label(server_frame, text="BAT窗口标题:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.bat_title_var = tk.StringVar(value=self.config.get("bat_title", "Minecraft Server"))
        bat_title_entry = ttk.Entry(server_frame, textvariable=self.bat_title_var)
        bat_title_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        bat_title_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # GUI选项
        ttk.Label(server_frame, text="GUI模式:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.gui_mode_var = tk.StringVar(value=self.config.get("gui_mode", "--nogui"))
        self.gui_mode_frame = ttk.Frame(server_frame)
        self.gui_mode_frame.grid(row=1, column=1, sticky=tk.W, pady=5, padx=(5, 0))

        self.gui_radio_nogui = ttk.Radiobutton(self.gui_mode_frame, text="无GUI (--nogui)",
                                             variable=self.gui_mode_var, value="--nogui",
                                             command=self.update_preview)
        self.gui_radio_nogui.pack(side=tk.LEFT, padx=(0, 10))

        self.gui_radio_gui = ttk.Radiobutton(self.gui_mode_frame, text="有GUI (默认)",
                                           variable=self.gui_mode_var, value="",
                                           command=self.update_preview)
        self.gui_radio_gui.pack(side=tk.LEFT)

        self.pause_var = tk.BooleanVar(value=self.config["pause_on_exit"])
        ttk.Checkbutton(server_frame, text="退出时暂停 (pause)",
                       variable=self.pause_var).grid(row=2, column=0, columnspan=2,
                                                    sticky=tk.W, pady=5)

        left_row += 1

        # 启动/关闭信息设置框架
        message_frame = ttk.LabelFrame(left_frame, text="📝 启动/关闭信息设置", padding="3")
        message_frame.grid(row=left_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        message_frame.columnconfigure(1, weight=1)

        # 启动信息选项
        self.show_startup_var = tk.BooleanVar()
        self.show_startup_var.set(True)  # 强制设置为勾选状态
        self.startup_checkbox = ttk.Checkbutton(message_frame, text="显示启动信息",
                       variable=self.show_startup_var,
                       command=self.update_preview)
        self.startup_checkbox.grid(row=0, column=0, sticky=tk.W, pady=2)

        ttk.Label(message_frame, text="自定义启动消息:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.startup_message_var = tk.StringVar(value=self.config.get("custom_startup_message", ""))
        startup_entry = ttk.Entry(message_frame, textvariable=self.startup_message_var)
        startup_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        startup_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # 关闭信息选项
        self.show_shutdown_var = tk.BooleanVar()
        self.show_shutdown_var.set(True)  # 强制设置为勾选状态
        self.shutdown_checkbox = ttk.Checkbutton(message_frame, text="显示关闭信息",
                       variable=self.show_shutdown_var,
                       command=self.update_preview)
        self.shutdown_checkbox.grid(row=2, column=0, sticky=tk.W, pady=5)

        ttk.Label(message_frame, text="自定义关闭消息:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.shutdown_message_var = tk.StringVar(value=self.config.get("custom_shutdown_message", ""))
        shutdown_entry = ttk.Entry(message_frame, textvariable=self.shutdown_message_var)
        shutdown_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        shutdown_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # 添加使用教程区域
        tutorial_frame = ttk.Frame(message_frame)
        tutorial_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 5))
        tutorial_frame.columnconfigure(0, weight=1)

        # 教程标题
        tutorial_title = ttk.Label(tutorial_frame, text="💡 启动/关闭信息设置说明",
                                  font=("Microsoft YaHei", 9, "bold"), foreground="#2c3e50")
        tutorial_title.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))

        # 教程内容
        tutorial_text = """• 显示启动信息：勾选后BAT文件会显示服务器启动提示
• 自定义启动消息：可设置个性化的启动欢迎信息
• 显示关闭信息：勾选后服务器关闭时会显示提示信息
• 自定义关闭消息：可设置服务器关闭时的个性化信息
• 留空自定义消息将使用默认的启动/关闭提示"""

        tutorial_label = ttk.Label(tutorial_frame, text=tutorial_text,
                                  font=("Microsoft YaHei", 8), foreground="#34495e",
                                  wraplength=380, justify=tk.LEFT)
        tutorial_label.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        left_row += 1

        # 高级设置框架（移到右侧）
        advanced_frame = ttk.LabelFrame(right_frame, text="🔧 高级设置 (可选)", padding="3")
        advanced_frame.grid(row=right_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        advanced_frame.columnconfigure(1, weight=1)

        # Aikar's Flags设置
        self.use_aikar_flags_var = tk.BooleanVar(value=self.config.get("use_aikar_flags", False))
        aikar_checkbox = ttk.Checkbutton(advanced_frame, text="🚀 使用Aikar's Flags (高性能优化)",
                                       variable=self.use_aikar_flags_var,
                                       command=self.toggle_aikar_flags)
        aikar_checkbox.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=3)

        # Aikar's Flags详细配置按钮
        self.aikar_config_btn = ttk.Button(advanced_frame, text="⚙️ 详细配置",
                                         command=self.open_aikar_config)
        self.aikar_config_btn.grid(row=0, column=2, pady=6, padx=(5, 0))

        # 自动重启设置
        self.auto_restart_var = tk.BooleanVar(value=self.config.get("auto_restart", False))
        ttk.Checkbutton(advanced_frame, text="🔄 服务器自动重启",
                       variable=self.auto_restart_var,
                       command=self.update_preview).grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=2)

        # JVM参数设置
        self.use_advanced_jvm_var = tk.BooleanVar(value=self.config.get("use_advanced_jvm", False))
        self.jvm_checkbox = ttk.Checkbutton(advanced_frame, text="✅ 使用自定义JVM参数 (内存优化、垃圾回收等)",
                                          variable=self.use_advanced_jvm_var,
                                          command=self.toggle_jvm_args)
        self.jvm_checkbox.grid(row=2, column=0, columnspan=2, sticky=tk.W, pady=2)

        # JVM参数说明
        jvm_info_label = ttk.Label(advanced_frame, text="💡 JVM参数用于优化Java虚拟机性能，如内存分配、垃圾回收策略等",
                                 foreground="blue", font=("Microsoft YaHei", 9), wraplength=460)
        jvm_info_label.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=4)

        ttk.Label(advanced_frame, text="自定义JVM参数:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.jvm_args_var = tk.StringVar(value=self.config["additional_jvm_args"])
        self.jvm_entry = ttk.Entry(advanced_frame, textvariable=self.jvm_args_var)
        self.jvm_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        self.jvm_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # 常用JVM参数快速选择
        jvm_quick_frame = ttk.Frame(advanced_frame)
        jvm_quick_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        ttk.Button(jvm_quick_frame, text="📋 常用参数",
                  command=self.show_common_jvm_args).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(jvm_quick_frame, text="🧹 清空",
                  command=lambda: self.jvm_args_var.set("")).pack(side=tk.LEFT)

        # 自定义启动参数设置
        self.use_custom_args_var = tk.BooleanVar(value=self.config.get("use_custom_args", False))
        self.args_checkbox = ttk.Checkbutton(advanced_frame, text="✅ 使用自定义启动参数 (服务器特殊配置)",
                                           variable=self.use_custom_args_var,
                                           command=self.toggle_custom_args)
        self.args_checkbox.grid(row=6, column=0, columnspan=2, sticky=tk.W, pady=2)

        # 启动参数说明
        args_info_label = ttk.Label(advanced_frame, text="💡 启动参数用于配置Minecraft服务器特殊功能，如调试模式、特殊端口等",
                                  foreground="blue", font=("Microsoft YaHei", 9), wraplength=460)
        args_info_label.grid(row=7, column=0, columnspan=3, sticky=tk.W, pady=4)

        ttk.Label(advanced_frame, text="自定义启动参数:").grid(row=8, column=0, sticky=tk.W, pady=2)
        self.additional_args_var = tk.StringVar(value=self.config["additional_args"])
        self.args_entry = ttk.Entry(advanced_frame, textvariable=self.additional_args_var)
        self.args_entry.grid(row=8, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 0))
        self.args_entry.bind('<KeyRelease>', lambda e: self.update_preview())

        # 常用启动参数快速选择
        args_quick_frame = ttk.Frame(advanced_frame)
        args_quick_frame.grid(row=9, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=2)

        ttk.Button(args_quick_frame, text="📋 常用参数",
                  command=self.show_common_server_args).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(args_quick_frame, text="🧹 清空",
                  command=lambda: self.additional_args_var.set("")).pack(side=tk.LEFT)

        # 编码支持设置
        encoding_support_frame = ttk.Frame(advanced_frame)
        encoding_support_frame.grid(row=10, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)

        # 添加编码支持选项
        self.use_chcp_var = tk.BooleanVar(value=self.config.get("use_chcp", True))
        ttk.Checkbutton(encoding_support_frame, text="🌐 使用 chcp 65001 (UTF-8控制台编码)",
                       variable=self.use_chcp_var,
                       command=self.update_preview).pack(anchor=tk.W, pady=2)

        self.use_file_encoding_var = tk.BooleanVar(value=self.config.get("use_file_encoding", True))
        ttk.Checkbutton(encoding_support_frame, text="📝 使用 -Dfile.encoding=UTF-8 (Java文件编码，插件中文支持)",
                       variable=self.use_file_encoding_var,
                       command=self.update_preview).pack(anchor=tk.W, pady=2)

        # 文件编码设置
        ttk.Label(advanced_frame, text="BAT文件编码:").grid(row=11, column=0, sticky=tk.W, pady=5)

        # 编码说明
        encoding_info_label = ttk.Label(advanced_frame, text="💡 BAT文件编码：UTF-8(推荐，支持中文)，ANSI(系统默认)，GBK(中文Windows)",
                                      foreground="blue", font=("Microsoft YaHei", 9), wraplength=460)
        encoding_info_label.grid(row=12, column=0, columnspan=3, sticky=tk.W, pady=4)

        self.encoding_var = tk.StringVar(value=self.config.get("file_encoding", "utf-8"))
        encoding_frame = ttk.Frame(advanced_frame)
        encoding_frame.grid(row=13, column=0, columnspan=3, sticky=tk.W, pady=5)

        ttk.Radiobutton(encoding_frame, text="UTF-8 (推荐)",
                       variable=self.encoding_var, value="utf-8",
                       command=self.update_preview).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(encoding_frame, text="ANSI (系统默认)",
                       variable=self.encoding_var, value="ansi",
                       command=self.update_preview).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(encoding_frame, text="GBK (中文Windows)",
                       variable=self.encoding_var, value="gbk",
                       command=self.update_preview).pack(side=tk.LEFT)

        right_row += 1

        # 输出文件设置（移到右侧）
        output_frame = ttk.LabelFrame(right_frame, text="📁 输出设置", padding="3")
        output_frame.grid(row=right_row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=3)
        output_frame.columnconfigure(1, weight=1)

        ttk.Label(output_frame, text="输出文件名:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.output_file_var = tk.StringVar(value=self.config["output_file"])
        ttk.Entry(output_frame, textvariable=self.output_file_var).grid(
            row=0, column=1, sticky=(tk.W, tk.E), pady=2, padx=(5, 5))
        ttk.Button(output_frame, text="选择位置",
                  command=self.browse_output).grid(row=0, column=2, pady=2)

        right_row += 1

        # 按钮框架（移到右侧底部）
        button_frame = tk.Frame(right_frame, bg='#f0f0f0')
        button_frame.grid(row=right_row, column=0, columnspan=3, pady=3, sticky=(tk.W, tk.E))

        # 创建美观的按钮 - 分两行显示
        button_row1 = tk.Frame(button_frame, bg='#f0f0f0')
        button_row1.pack(pady=3)

        ttk.Button(button_row1, text="🔄 更新预览",
                  command=self.update_preview, style='Custom.TButton').pack(side=tk.LEFT, padx=5)
        # 生成按钮移到保存配置按钮的位置
        generate_btn = ttk.Button(button_row1, text="🚀 保存BAT文件",
                                command=self.generate_and_save_bat, style='Custom.TButton')
        generate_btn.pack(side=tk.LEFT, padx=5)
        ttk.Button(button_row1, text="📂 加载BAT文件",
                  command=self.load_config_file, style='Custom.TButton').pack(side=tk.LEFT, padx=5)

        button_row2 = tk.Frame(button_frame, bg='#f0f0f0')
        button_row2.pack(pady=3)

        # 为生成按钮设置特殊样式
        style = ttk.Style()
        style.configure('Generate.TButton',
                       font=('Microsoft YaHei', 10, 'bold'),
                       padding=(15, 8))
        style.map('Generate.TButton',
                 background=[('active', '#27ae60'),
                           ('pressed', '#229954')])
        generate_btn.configure(style='Generate.TButton')

        # 预览框架（移到中间）
        preview_frame = ttk.LabelFrame(middle_frame, text="📄 BAT文件预览 (可编辑) - 💡 可使用'加载BAT文件'按钮导入现有文件", padding="3")
        preview_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=3)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(1, weight=1)
        preview_frame.configure(style='Preview.TLabelframe')

        # 预览控制按钮
        preview_control_frame = ttk.Frame(preview_frame)
        preview_control_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        self.preview_editable_var = tk.BooleanVar(value=self.config.get("preview_editable", True))
        ttk.Checkbutton(preview_control_frame, text="✏️ 允许编辑预览",
                       variable=self.preview_editable_var,
                       command=self.toggle_preview_edit).pack(side=tk.LEFT)

        ttk.Button(preview_control_frame, text="🔄 重新生成",
                  command=self.regenerate_preview).pack(side=tk.LEFT, padx=(10, 0))

        ttk.Button(preview_control_frame, text="📋 复制到剪贴板",
                  command=self.copy_to_clipboard).pack(side=tk.LEFT, padx=(5, 0))

        # 预览文本框
        self.preview_text = tk.Text(preview_frame, height=20, width=85, wrap=tk.WORD,
                                  font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=scrollbar.set)

        self.preview_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))

        # 初始化设置
        self.toggle_jvm_args()
        self.toggle_custom_args()
        self.toggle_aikar_flags()
        self.toggle_preview_edit()
        self.update_version_info()
        self.update_gui_options()

        # 强制确保复选框状态正确
        self.root.after(100, self.force_checkbox_state)

        self.update_preview()

    def force_checkbox_state(self):
        """强制设置复选框状态为勾选"""
        try:
            # 强制设置启动信息复选框为勾选状态
            self.show_startup_var.set(True)
            self.startup_checkbox.invoke()  # 触发一次以确保状态更新
            self.startup_checkbox.invoke()  # 再次触发回到勾选状态

            # 强制设置关闭信息复选框为勾选状态
            self.show_shutdown_var.set(True)
            self.shutdown_checkbox.invoke()  # 触发一次以确保状态更新
            self.shutdown_checkbox.invoke()  # 再次触发回到勾选状态

            print("强制设置复选框状态完成")
        except Exception as e:
            print(f"设置复选框状态时出错: {e}")

    def setup_drag_drop(self, widget, file_type):
        """设置拖放功能"""
        if HAS_DND:
            # 如果有tkinterdnd2支持，使用完整的拖放功能
            try:
                widget.drop_target_register(DND_FILES)
                if file_type == "java":
                    widget.dnd_bind('<<Drop>>', self.on_java_drop)
                elif file_type == "jar":
                    widget.dnd_bind('<<Drop>>', self.on_jar_drop)
            except Exception as e:
                print(f"拖放设置失败: {e}")
                self.setup_basic_drag_drop(widget, file_type)
        else:
            # 使用基本的拖放功能（Windows原生支持）
            self.setup_basic_drag_drop(widget, file_type)

    def setup_basic_drag_drop(self, widget, file_type):
        """设置基本拖放功能（不依赖外部库）"""
        def on_drop(event):
            # 获取拖放的文件路径
            files = event.data
            if files:
                # 处理文件路径
                if files.startswith('{') and files.endswith('}'):
                    files = files[1:-1]  # 移除大括号

                file_path = files.strip()

                if file_type == "java":
                    if file_path.lower().endswith(('.exe', '.jar')) or 'java' in file_path.lower():
                        self.java_path_var.set(file_path)
                        self.update_preview()
                    else:
                        messagebox.showwarning("文件类型错误", "请拖放Java可执行文件")
                elif file_type == "jar":
                    if file_path.lower().endswith('.jar'):
                        self.jar_file_var.set(file_path)
                        if self.auto_detect_var.get():
                            self.detect_version_from_jar(file_path)
                        self.update_preview()
                    else:
                        messagebox.showwarning("文件类型错误", "请拖放JAR文件")

        # 绑定拖放事件
        widget.bind('<Button-1>', lambda e: widget.focus_set())

        # 添加右键提示
        def show_drag_tip(event):
            tip_msg = f"拖放提示:\n"
            if file_type == "java":
                tip_msg += "• 直接拖放Java可执行文件到此输入框\n"
                tip_msg += "• 或使用'浏览'/'搜索'按钮选择"
            else:
                tip_msg += "• 直接拖放JAR文件到此输入框\n"
                tip_msg += "• 或使用'浏览'按钮选择"

            if not HAS_DND:
                tip_msg += "\n\n要启用完整拖放功能，请运行:\npip install tkinterdnd2"

            messagebox.showinfo("拖放功能", tip_msg)

        widget.bind('<Button-3>', show_drag_tip)

    def setup_windows_drag_drop(self, widget, file_type):
        """设置Windows原生拖放功能"""
        try:
            # 简化的拖放支持 - 使用tkinter的内置功能
            def on_drag_enter(event):
                widget.config(bg='lightblue')
                return 'copy'

            def on_drag_leave(event):
                widget.config(bg='white')

            def on_drop(event):
                widget.config(bg='white')
                # 尝试从剪贴板获取文件路径
                try:
                    file_path = self.root.clipboard_get()
                    if os.path.exists(file_path):
                        self.handle_dropped_file(file_path, file_type)
                except:
                    # 如果剪贴板方法失败，显示手动输入提示
                    result = messagebox.askquestion("拖放文件",
                        f"检测到拖放操作。\n请手动输入{file_type}文件路径，或使用浏览按钮选择文件。\n\n是否打开文件选择对话框？")
                    if result == 'yes':
                        if file_type == "java":
                            self.browse_java()
                        elif file_type == "jar":
                            self.browse_jar()

            # 绑定拖放事件（简化版本）
            widget.bind('<Button-1>', lambda e: widget.focus_set())
            widget.bind('<Double-Button-1>', on_drop)

            print(f"为 {file_type} 输入框启用了简化拖放支持（双击触发）")

        except Exception as e:
            print(f"拖放设置失败: {e}")

    def handle_dropped_file(self, file_path, file_type):
        """处理拖放的文件"""
        try:
            if file_type == "java":
                if file_path.lower().endswith(('.exe', '.jar')) or 'java' in file_path.lower():
                    self.java_path_var.set(file_path)
                    self.update_preview()
                    messagebox.showinfo("设置成功", f"已设置Java路径: {os.path.basename(file_path)}")
                else:
                    messagebox.showwarning("文件类型错误", "请选择Java可执行文件")
            elif file_type == "jar":
                if file_path.lower().endswith('.jar'):
                    self.jar_file_var.set(file_path)
                    if self.auto_detect_var.get():
                        self.detect_version_from_jar(file_path)
                    self.update_preview()
                    messagebox.showinfo("设置成功", f"已设置JAR文件: {os.path.basename(file_path)}")
                else:
                    messagebox.showwarning("文件类型错误", "请选择JAR文件")
        except Exception as e:
            print(f"文件处理错误: {e}")
            messagebox.showerror("错误", f"处理文件时出错: {e}")



    def on_java_drop(self, event):
        """处理Java文件拖放"""
        files = self.parse_drop_files(event.data)
        if files:
            file_path = files[0]
            if file_path.lower().endswith(('.exe', '.jar')) or 'java' in file_path.lower():
                self.java_path_var.set(file_path)
                self.update_preview()
            else:
                messagebox.showwarning("文件类型错误", "请拖放Java可执行文件 (.exe) 或包含'java'的文件")

    def on_jar_drop(self, event):
        """处理JAR文件拖放"""
        files = self.parse_drop_files(event.data)
        if files:
            file_path = files[0]
            if file_path.lower().endswith('.jar'):
                self.jar_file_var.set(file_path)
                if self.auto_detect_var.get():
                    self.detect_version_from_jar(file_path)
                self.update_preview()
            else:
                messagebox.showwarning("文件类型错误", "请拖放JAR文件 (.jar)")

    def parse_drop_files(self, data):
        """解析拖放的文件数据"""
        # 处理拖放数据，可能包含多个文件
        files = []
        if data:
            # 移除大括号并分割文件路径
            data = data.strip('{}')
            files = [f.strip() for f in data.split('}') if f.strip()]
        return files

    def browse_java(self):
        """浏览选择Java可执行文件"""
        filename = filedialog.askopenfilename(
            title="选择Java可执行文件",
            filetypes=[("可执行文件", "*.exe"), ("所有文件", "*.*")]
        )
        if filename:
            self.java_path_var.set(filename)
            self.update_preview()

    def browse_jar(self):
        """浏览选择服务器JAR文件"""
        filename = filedialog.askopenfilename(
            title="选择Minecraft服务器JAR文件",
            filetypes=[("JAR文件", "*.jar"), ("所有文件", "*.*")]
        )
        if filename:
            self.jar_file_var.set(filename)
            if self.auto_detect_var.get():
                self.detect_version_from_jar(filename)
            self.update_preview()

    def browse_output(self):
        """选择输出BAT文件位置"""
        filename = filedialog.asksaveasfilename(
            title="保存BAT文件",
            defaultextension=".bat",
            filetypes=[("批处理文件", "*.bat"), ("所有文件", "*.*")]
        )
        if filename:
            self.output_file_var.set(filename)

    def update_preview(self):
        """更新BAT文件预览"""
        bat_content = self.generate_bat_content()
        self.preview_text.delete(1.0, tk.END)
        self.preview_text.insert(1.0, bat_content)

    def toggle_jvm_args(self):
        """切换JVM参数输入框的启用状态"""
        if self.use_advanced_jvm_var.get():
            self.jvm_entry.config(state="normal")
        else:
            self.jvm_entry.config(state="disabled")
        self.update_preview()

    def toggle_custom_args(self):
        """切换自定义参数输入框的启用状态"""
        if self.use_custom_args_var.get():
            self.args_entry.config(state="normal")
        else:
            self.args_entry.config(state="disabled")
        self.update_preview()

    def toggle_aikar_flags(self):
        """切换Aikar's Flags的启用状态"""
        if self.use_aikar_flags_var.get():
            self.aikar_config_btn.config(state="normal")
        else:
            self.aikar_config_btn.config(state="disabled")
        self.update_preview()

    def open_aikar_config(self):
        """打开Aikar's Flags详细配置窗口"""
        config_window = tk.Toplevel(self.root)
        config_window.title("⚙️ Aikar's Flags 详细配置")
        config_window.geometry("800x600")
        config_window.transient(self.root)
        config_window.grab_set()

        # 创建主框架
        main_frame = ttk.Frame(config_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="🚀 Aikar's Flags 高性能优化参数配置",
                               font=("Microsoft YaHei", 14, "bold"))
        title_label.pack(pady=(0, 10))

        # 说明文字
        info_text = """Aikar's Flags 是由 Aikar 开发的一套高性能 JVM 参数，专门针对 Minecraft 服务器优化。
这些参数可以显著提升服务器性能，减少延迟和卡顿。建议内存 ≥ 4GB 的服务器使用。"""

        info_label = ttk.Label(main_frame, text=info_text, wraplength=750,
                              font=("Microsoft YaHei", 10), foreground="blue")
        info_label.pack(pady=(0, 15))

        # 创建滚动框架
        canvas = tk.Canvas(main_frame, bg='white')
        scrollbar = ttk.Scrollbar(main_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 初始化JVM选项状态（如果不存在）
        if not hasattr(self, 'aikar_options'):
            self.aikar_options = {}
            for key, option in self.jvm_options.items():
                self.aikar_options[key] = tk.BooleanVar(value=option.get("default", False))

        # 按类别组织参数
        categories = {}
        for key, option in self.jvm_options.items():
            category = option.get("category", "其他")
            if category not in categories:
                categories[category] = {}
            categories[category][key] = option

        row = 0
        for category, options in categories.items():
            # 类别框架
            category_frame = ttk.LabelFrame(scrollable_frame, text=f"📋 {category}参数", padding="10")
            category_frame.grid(row=row, column=0, sticky=(tk.W, tk.E), padx=10, pady=8)
            category_frame.columnconfigure(1, weight=1)

            option_row = 0
            for key, option in options.items():
                # 参数复选框
                checkbox = ttk.Checkbutton(category_frame, text=option["name"],
                                         variable=self.aikar_options[key])
                checkbox.grid(row=option_row, column=0, sticky=tk.W, pady=3)

                # 参数值（如果有）
                param_text = option["param"]
                if "unit" in option:
                    param_text += option.get("default", "") + " " + option["unit"]
                elif option.get("default") and not isinstance(option["default"], bool):
                    param_text += str(option["default"])

                param_label = ttk.Label(category_frame, text=param_text,
                                      font=("Consolas", 9), foreground="gray")
                param_label.grid(row=option_row, column=1, sticky=tk.W, padx=(20, 0), pady=3)

                option_row += 1
            row += 1

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        # 预设按钮
        preset_frame = ttk.LabelFrame(button_frame, text="🎯 快速预设", padding="5")
        preset_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(preset_frame, text="🚀 推荐配置 (4GB+)",
                  command=lambda: self.apply_aikar_preset("recommended")).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="⚡ 高性能配置 (8GB+)",
                  command=lambda: self.apply_aikar_preset("high_performance")).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="🔧 保守配置 (2GB+)",
                  command=lambda: self.apply_aikar_preset("conservative")).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="🧹 全部清除",
                  command=lambda: self.apply_aikar_preset("clear")).pack(side=tk.LEFT, padx=5)

        # 控制按钮
        control_frame = ttk.Frame(button_frame)
        control_frame.pack(fill=tk.X)

        ttk.Button(control_frame, text="✅ 应用配置",
                  command=lambda: self.apply_aikar_config(config_window)).pack(side=tk.RIGHT, padx=5)
        ttk.Button(control_frame, text="❌ 取消",
                  command=config_window.destroy).pack(side=tk.RIGHT, padx=5)

        # 绑定鼠标滚轮
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind("<MouseWheel>", _on_mousewheel)

    def apply_aikar_preset(self, preset_type):
        """应用Aikar预设配置"""
        if preset_type == "recommended":
            # 推荐配置 (4GB+)
            enabled_options = [
                "use_g1gc", "parallel_ref", "max_gc_pause", "unlock_experimental",
                "disable_explicit_gc", "always_pre_touch", "heap_waste_percent",
                "mixed_gc_count", "heap_occupancy", "aikars_flags", "new_flags"
            ]
        elif preset_type == "high_performance":
            # 高性能配置 (8GB+)
            enabled_options = [
                "use_g1gc", "parallel_ref", "max_gc_pause", "unlock_experimental",
                "disable_explicit_gc", "always_pre_touch", "heap_waste_percent",
                "mixed_gc_count", "heap_occupancy", "mixed_gc_threshold",
                "rset_updating_pause", "survivor_ratio", "perf_disable_shared",
                "max_tenuring", "g1_new_size", "g1_max_new_size", "g1_heap_region",
                "g1_reserve", "aikars_flags", "new_flags"
            ]
        elif preset_type == "conservative":
            # 保守配置 (2GB+)
            enabled_options = [
                "use_g1gc", "parallel_ref", "max_gc_pause", "disable_explicit_gc",
                "aikars_flags", "new_flags"
            ]
        else:  # clear
            enabled_options = []

        # 应用预设
        for key in self.aikar_options:
            self.aikar_options[key].set(key in enabled_options)

    def apply_aikar_config(self, config_window):
        """应用Aikar配置并关闭窗口"""
        # 生成选中的参数
        selected_flags = []
        for key, var in self.aikar_options.items():
            if var.get():
                option = self.jvm_options[key]
                param = option["param"]
                if "unit" in option and option.get("default"):
                    param += str(option["default"])
                elif option.get("default") and not isinstance(option["default"], bool):
                    param += str(option["default"])
                selected_flags.append(param)

        # 更新生成函数使用的标志
        self.custom_aikar_flags = selected_flags

        # 关闭窗口并更新预览
        config_window.destroy()
        self.update_preview()
        messagebox.showinfo("成功", f"已应用 {len(selected_flags)} 个Aikar's Flags参数")

    def toggle_preview_edit(self):
        """切换预览编辑模式"""
        if self.preview_editable_var.get():
            self.preview_text.config(state="normal", bg="white")
        else:
            self.preview_text.config(state="disabled", bg="#f0f0f0")

    def regenerate_preview(self):
        """重新生成预览内容"""
        self.update_preview()
        messagebox.showinfo("完成", "预览内容已重新生成")

    def copy_to_clipboard(self):
        """复制预览内容到剪贴板"""
        content = self.preview_text.get(1.0, tk.END)
        self.root.clipboard_clear()
        self.root.clipboard_append(content)
        messagebox.showinfo("成功", "内容已复制到剪贴板")

    def show_common_jvm_args(self):
        """显示常用JVM参数选择窗口"""
        args_window = tk.Toplevel(self.root)
        args_window.title("🔧 常用JVM参数")
        args_window.geometry("800x600")  # 增大窗口尺寸
        args_window.transient(self.root)
        args_window.grab_set()

        # 设置窗口居中
        args_window.update_idletasks()
        x = (args_window.winfo_screenwidth() // 2) - (800 // 2)
        y = (args_window.winfo_screenheight() // 2) - (600 // 2)
        args_window.geometry(f"800x600+{x}+{y}")

        # 常用JVM参数列表
        common_args = {
            "内存优化": {
                "大内存优化": "-XX:+UseG1GC -XX:+UnlockExperimentalVMOptions -XX:MaxGCPauseMillis=100",
                "小内存优化": "-XX:+UseSerialGC -Xms512M",
                "中等内存优化": "-XX:+UseParallelGC -XX:ParallelGCThreads=4"
            },
            "垃圾回收": {
                "G1垃圾回收器": "-XX:+UseG1GC -XX:G1HeapRegionSize=16M",
                "并行垃圾回收器": "-XX:+UseParallelGC -XX:+UseParallelOldGC",
                "CMS垃圾回收器": "-XX:+UseConcMarkSweepGC -XX:+CMSParallelRemarkEnabled"
            },
            "性能调优": {
                "禁用显式GC": "-XX:+DisableExplicitGC",
                "预分配内存": "-XX:+AlwaysPreTouch",
                "优化字符串": "-XX:+UseStringDeduplication"
            },
            "调试选项": {
                "GC日志": "-XX:+PrintGC -XX:+PrintGCDetails",
                "内存转储": "-XX:+HeapDumpOnOutOfMemoryError",
                "JVM崩溃日志": "-XX:+PrintCrashReports"
            }
        }

        # 创建主框架，移除滚动
        main_frame = ttk.Frame(args_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)

        # 按2x2网格布局显示类别
        categories_list = list(common_args.items())
        positions = [(0, 0), (0, 1), (1, 0), (1, 1)]

        for i, (category, args) in enumerate(categories_list):
            if i < len(positions):
                row, col = positions[i]
                # 类别框架
                category_frame = ttk.LabelFrame(main_frame, text=f"📋 {category}", padding="10")
                category_frame.grid(row=row, column=col, sticky=(tk.W, tk.E, tk.N, tk.S),
                                  padx=10, pady=10)
                category_frame.columnconfigure(0, weight=1)

                arg_row = 0
                for name, arg in args.items():
                    # 创建每个参数的框架
                    arg_frame = ttk.Frame(category_frame)
                    arg_frame.grid(row=arg_row, column=0, sticky=(tk.W, tk.E), pady=3)
                    arg_frame.columnconfigure(0, weight=1)

                    # 参数名称标签
                    ttk.Label(arg_frame, text=name, font=("Microsoft YaHei", 10)).grid(
                        row=0, column=0, sticky=tk.W)

                    # 使用按钮
                    ttk.Button(arg_frame, text="使用", width=8,
                              command=lambda a=arg: self.apply_jvm_arg(a, args_window)).grid(
                        row=0, column=1, sticky=tk.E, padx=(5, 0))

                    arg_row += 1

        # 底部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=2, pady=(20, 0))

        ttk.Button(button_frame, text="❌ 关闭", command=args_window.destroy,
                  style='Custom.TButton').pack()

    def show_common_server_args(self):
        """显示常用服务器启动参数选择窗口"""
        args_window = tk.Toplevel(self.root)
        args_window.title("🎮 常用服务器参数")
        args_window.geometry("800x600")  # 增大窗口尺寸
        args_window.transient(self.root)
        args_window.grab_set()

        # 设置窗口居中
        args_window.update_idletasks()
        x = (args_window.winfo_screenwidth() // 2) - (800 // 2)
        y = (args_window.winfo_screenheight() // 2) - (600 // 2)
        args_window.geometry(f"800x600+{x}+{y}")

        # 常用服务器参数（只保留真正实用的调试选项）
        common_args = {
            "调试与分析": {
                "详细日志": "--verbose",
                "调试模式": "--debug",
                "性能分析": "--profiler",
                "JVM调试端口": "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005",
                "JVM详细GC": "-verbose:gc",
                "JVM类加载信息": "-verbose:class"
            }
        }

        # 创建主框架，移除滚动
        main_frame = ttk.Frame(args_window, padding="15")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 配置网格权重 - 单个类别居中显示
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=1)

        # 只有一个类别，居中显示
        category, args = list(common_args.items())[0]

        # 类别框架
        category_frame = ttk.LabelFrame(main_frame, text=f"📋 {category}", padding="15")
        category_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S),
                          padx=50, pady=30)
        category_frame.columnconfigure(0, weight=1)

        arg_row = 0
        for name, arg in args.items():
            # 创建每个参数的框架
            arg_frame = ttk.Frame(category_frame)
            arg_frame.grid(row=arg_row, column=0, sticky=(tk.W, tk.E), pady=5)
            arg_frame.columnconfigure(0, weight=1)

            # 参数名称标签
            ttk.Label(arg_frame, text=name, font=("Microsoft YaHei", 11)).grid(
                row=0, column=0, sticky=tk.W)

            # 使用按钮
            ttk.Button(arg_frame, text="使用", width=10,
                      command=lambda a=arg: self.apply_server_arg(a, args_window)).grid(
                row=0, column=1, sticky=tk.E, padx=(10, 0))

            arg_row += 1

        # 底部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=1, column=0, pady=(20, 0))

        ttk.Button(button_frame, text="❌ 关闭", command=args_window.destroy,
                  style='Custom.TButton').pack()

    def apply_jvm_arg(self, arg, window):
        """应用JVM参数"""
        current = self.jvm_args_var.get()
        if current:
            self.jvm_args_var.set(f"{current} {arg}")
        else:
            self.jvm_args_var.set(arg)
        self.update_preview()
        window.destroy()
        messagebox.showinfo("成功", f"已添加JVM参数: {arg}")

    def apply_server_arg(self, arg, window):
        """应用服务器参数"""
        current = self.additional_args_var.get()
        if current:
            self.additional_args_var.set(f"{current} {arg}")
        else:
            self.additional_args_var.set(arg)
        self.update_preview()
        window.destroy()
        messagebox.showinfo("成功", f"已添加服务器参数: {arg}")

    def generate_and_save_bat(self):
        """保存预览区域的BAT文件内容"""
        output_file = self.output_file_var.get().strip()
        if not output_file:
            messagebox.showerror("错误", "请设置输出文件名")
            return

        # 确保文件扩展名为.bat
        if not output_file.lower().endswith('.bat'):
            output_file += '.bat'

        try:
            # 获取预览区域的内容（可能已被手动编辑）
            bat_content = self.preview_text.get(1.0, tk.END)
            # 移除末尾的换行符
            bat_content = bat_content.rstrip('\n')

            encoding = self.encoding_var.get()

            # 根据选择的编码保存文件
            if encoding == "utf-8":
                with open(output_file, 'w', encoding='utf-8-sig') as f:  # 使用BOM
                    f.write(bat_content)
            elif encoding == "gbk":
                with open(output_file, 'w', encoding='gbk') as f:
                    f.write(bat_content)
            else:  # ansi
                with open(output_file, 'w', encoding='cp1252') as f:
                    f.write(bat_content)

            messagebox.showinfo("成功", f"BAT文件已保存到: {output_file}")

            # 询问是否打开文件所在目录
            if messagebox.askyesno("打开目录", "是否打开文件所在目录？"):
                import subprocess
                subprocess.run(['explorer', '/select,', os.path.abspath(output_file)])

        except Exception as e:
            messagebox.showerror("错误", f"保存文件失败: {e}")

    def generate_aikar_flags(self):
        """生成Aikar's Flags参数"""
        # 如果有自定义配置，使用自定义配置
        if hasattr(self, 'custom_aikar_flags') and self.custom_aikar_flags:
            return self.custom_aikar_flags

        # 否则使用默认的完整Aikar's Flags
        flags = [
            "-XX:+UseG1GC",
            "-XX:+ParallelRefProcEnabled",
            "-XX:MaxGCPauseMillis=200",
            "-XX:+UnlockExperimentalVMOptions",
            "-XX:+DisableExplicitGC",
            "-XX:+AlwaysPreTouch",
            "-XX:G1HeapWastePercent=5",
            "-XX:G1MixedGCCountTarget=4",
            "-XX:InitiatingHeapOccupancyPercent=15",
            "-XX:G1MixedGCLiveThresholdPercent=90",
            "-XX:G1RSetUpdatingPauseTimePercent=5",
            "-XX:SurvivorRatio=32",
            "-XX:+PerfDisableSharedMem",
            "-XX:MaxTenuringThreshold=1",
            "-Dusing.aikars.flags=https://mcflags.emc.gs",
            "-Daikars.new.flags=true",
            "-XX:G1NewSizePercent=30",
            "-XX:G1MaxNewSizePercent=40",
            "-XX:G1HeapRegionSize=8M",
            "-XX:G1ReservePercent=20"
        ]
        return flags

    def generate_bat_content(self):
        """生成BAT文件内容"""
        java_path = self.java_path_var.get().strip()
        jar_file = self.jar_file_var.get().strip()
        min_memory = self.min_memory_var.get().strip()
        max_memory = self.max_memory_var.get().strip()

        jvm_args = self.jvm_args_var.get().strip()
        additional_args = self.additional_args_var.get().strip()
        version_key = self.version_var.get()
        gui_mode = self.gui_mode_var.get()

        # 获取版本信息
        version_info = self.minecraft_versions.get(version_key, self.minecraft_versions["1.20+"])

        # 构建命令行
        cmd_parts = []

        # 添加注释头
        bat_content = "@echo off\n"
        bat_title = self.bat_title_var.get().strip() or "Minecraft Server"
        bat_content += f"title {bat_title}\n"

        # 添加编码支持
        if self.use_chcp_var.get():
            bat_content += "chcp 65001 >nul\n"  # 设置UTF-8控制台编码

        bat_content += "\n"

        # 启动信息
        if self.show_startup_var.get():
            custom_startup = self.startup_message_var.get().strip()
            if custom_startup:
                bat_content += f"echo {custom_startup}\n"
            else:
                bat_content += f"echo Starting {version_info['name']}...\n"
                bat_content += f"echo Required: {version_info['min_java']}\n"
            bat_content += "echo.\n"

        bat_content += "\n"

        # Java路径
        if java_path:
            cmd_parts.append(f'"{java_path}"')
        else:
            cmd_parts.append("java")

        # 内存设置
        if min_memory:
            cmd_parts.append(f"-Xms{min_memory}")
        if max_memory:
            cmd_parts.append(f"-Xmx{max_memory}")

        # 文件编码支持
        if self.use_file_encoding_var.get():
            cmd_parts.append("-Dfile.encoding=UTF-8")

        # Aikar's Flags (如果启用)
        if self.use_aikar_flags_var.get():
            aikar_flags = self.generate_aikar_flags()
            cmd_parts.extend(aikar_flags)

        # 额外JVM参数 (仅在启用时添加)
        if self.use_advanced_jvm_var.get() and jvm_args:
            cmd_parts.append(jvm_args)

        # JAR文件
        cmd_parts.append("-jar")
        if jar_file:
            cmd_parts.append(f'"{jar_file}"')
        else:
            cmd_parts.append("server.jar")



        # GUI设置 (根据版本支持情况)
        if gui_mode == "--nogui" and version_info["supports_nogui"]:
            cmd_parts.append("--nogui")
        elif gui_mode == "" and not version_info["supports_gui"]:
            # 如果版本不支持GUI但用户选择了GUI，添加警告注释
            bat_content += "REM Warning: This version does not support GUI mode\n"

        # 额外参数 (仅在启用时添加)
        if self.use_custom_args_var.get() and additional_args:
            cmd_parts.append(additional_args)

        # 自动重启功能标签
        if self.auto_restart_var.get():
            bat_content += ":start\n"

        # 组合命令
        bat_content += " ".join(cmd_parts) + "\n"

        # 自动重启逻辑
        if self.auto_restart_var.get():
            bat_content += "\necho.\n"
            bat_content += "echo 正在重启服务器...\n"
            bat_content += "echo 按下 CTRL + C 以暂停\n"
            bat_content += "timeout /t 5 /nobreak >nul\n"
            bat_content += "goto :start\n\n"

        # 添加关闭信息和暂停
        if self.show_shutdown_var.get() or self.pause_var.get():
            bat_content += "\necho.\n"

            if self.show_shutdown_var.get():
                custom_shutdown = self.shutdown_message_var.get().strip()
                if custom_shutdown:
                    bat_content += f"echo {custom_shutdown}\n"
                else:
                    bat_content += "echo Server stopped.\n"

            if self.pause_var.get():
                bat_content += "echo Press any key to exit...\n"
                bat_content += "pause >nul\n"

        return bat_content

    def save_config(self):
        """保存当前配置到JSON文件"""
        config = {
            "java_path": self.java_path_var.get(),
            "jar_file": self.jar_file_var.get(),
            "minecraft_version": self.version_var.get(),
            "min_memory": self.min_memory_var.get(),
            "max_memory": self.max_memory_var.get(),

            "gui_mode": self.gui_mode_var.get(),
            "additional_jvm_args": self.jvm_args_var.get(),
            "additional_args": self.additional_args_var.get(),
            "pause_on_exit": self.pause_var.get(),
            "output_file": self.output_file_var.get(),
            "auto_detect_version": self.auto_detect_var.get(),
            "show_startup_info": self.show_startup_var.get(),
            "show_shutdown_info": self.show_shutdown_var.get(),
            "custom_startup_message": self.startup_message_var.get(),
            "custom_shutdown_message": self.shutdown_message_var.get(),
            "bat_title": self.bat_title_var.get(),
            "use_advanced_jvm": self.use_advanced_jvm_var.get(),
            "use_custom_args": self.use_custom_args_var.get(),
            "file_encoding": self.encoding_var.get(),
            "use_aikar_flags": self.use_aikar_flags_var.get(),
            "auto_restart": self.auto_restart_var.get(),
            "preview_editable": self.preview_editable_var.get(),
            "use_chcp": self.use_chcp_var.get(),
            "use_file_encoding": self.use_file_encoding_var.get()
        }

        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("成功", f"配置已保存到 {self.config_file}")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def load_config(self):
        """从JSON文件加载配置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                return {**self.default_config, **config}
            except Exception as e:
                messagebox.showerror("错误", f"加载配置失败: {str(e)}")
                return self.default_config
        return self.default_config

    def load_config_file(self):
        """加载BAT文件到预览区域"""
        filename = filedialog.askopenfilename(
            title="选择BAT文件",
            filetypes=[("批处理文件", "*.bat"), ("命令文件", "*.cmd"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                # 尝试多种编码读取BAT文件
                bat_content = None
                encodings_to_try = ['utf-8-sig', 'utf-8', 'gbk', 'cp936', 'ansi', 'latin1']

                for encoding in encodings_to_try:
                    try:
                        with open(filename, 'r', encoding=encoding) as f:
                            bat_content = f.read()
                        print(f"成功使用 {encoding} 编码读取文件")
                        break
                    except (UnicodeDecodeError, UnicodeError):
                        continue

                if bat_content is None:
                    # 如果所有编码都失败，尝试二进制读取并转换
                    with open(filename, 'rb') as f:
                        raw_content = f.read()
                    try:
                        bat_content = raw_content.decode('utf-8', errors='replace')
                    except:
                        bat_content = raw_content.decode('latin1', errors='replace')

                # 将BAT文件内容显示在预览区域
                self.preview_text.delete(1.0, tk.END)
                self.preview_text.insert(1.0, bat_content)

                # 设置输出文件名为加载的文件名（去掉路径）
                base_filename = os.path.basename(filename)
                self.output_file_var.set(base_filename)

                messagebox.showinfo("成功", f"BAT文件已加载到预览区域\n文件: {base_filename}")

            except Exception as e:
                messagebox.showerror("错误", f"加载BAT文件失败: {str(e)}")

    def generate_bat(self):
        """生成并保存BAT文件"""
        output_file = self.output_file_var.get().strip()
        if not output_file:
            messagebox.showerror("错误", "请指定输出文件名")
            return

        try:
            bat_content = self.generate_bat_content()

            # 确保文件扩展名为.bat
            if not output_file.lower().endswith('.bat'):
                output_file += '.bat'

            # 根据选择的编码保存文件
            encoding = self.encoding_var.get()
            if encoding == "ansi":
                # ANSI编码 (Windows系统默认编码，通常是CP936/GBK)
                try:
                    with open(output_file, 'w', encoding='cp936') as f:
                        f.write(bat_content)
                except UnicodeEncodeError:
                    # 如果CP936编码失败，尝试使用GBK
                    with open(output_file, 'w', encoding='gbk') as f:
                        f.write(bat_content)
            elif encoding == "gbk":
                # GBK编码 (中文Windows)
                with open(output_file, 'w', encoding='gbk') as f:
                    f.write(bat_content)
            else:
                # UTF-8编码 (推荐)
                with open(output_file, 'w', encoding='utf-8-sig') as f:  # 添加BOM以确保Windows兼容性
                    f.write(bat_content)

            messagebox.showinfo("成功", f"BAT文件已生成: {output_file}")

            # 询问是否打开文件所在目录
            if messagebox.askyesno("打开目录", "是否打开文件所在目录?"):
                import subprocess
                import platform

                file_path = os.path.abspath(output_file)
                if platform.system() == "Windows":
                    subprocess.run(f'explorer /select,"{file_path}"', shell=True)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", "-R", file_path])
                else:  # Linux
                    subprocess.run(["xdg-open", os.path.dirname(file_path)])

        except Exception as e:
            messagebox.showerror("错误", f"生成BAT文件失败: {str(e)}")

    def detect_version_from_jar(self, jar_path):
        """从JAR文件名自动检测Minecraft版本"""
        jar_name = os.path.basename(jar_path).lower()

        # 检测服务器类型
        if "paper" in jar_name:
            detected_version = "paper"
        elif "spigot" in jar_name:
            detected_version = "spigot"
        elif "forge" in jar_name:
            detected_version = "forge"
        elif "fabric" in jar_name:
            detected_version = "fabric"
        else:
            # 检测版本号
            import re
            version_patterns = [
                (r"1\.20", "1.20+"),
                (r"1\.1[789]", "1.17-1.19"),
                (r"1\.1[3456]", "1.13-1.16"),
                (r"1\.[789]|1\.1[012]", "1.7-1.12"),
                (r"1\.[0-6]", "1.6及以下")
            ]

            detected_version = "1.20+"  # 默认
            for pattern, version in version_patterns:
                if re.search(pattern, jar_name):
                    detected_version = version
                    break

        self.version_var.set(detected_version)
        self.on_version_change()

    def on_version_change(self, event=None):
        """版本改变时的回调"""
        # event参数用于tkinter回调，可能为None
        self.update_version_info()
        self.update_gui_options()
        self.update_jvm_args()
        self.update_preview()

    def on_auto_detect_change(self):
        """自动检测选项改变时的回调"""
        if self.auto_detect_var.get() and self.jar_file_var.get():
            self.detect_version_from_jar(self.jar_file_var.get())

    def update_version_info(self):
        """更新版本信息显示"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]
            info_text = f"{version_info['name']} - 需要 {version_info['min_java']}"
            self.version_info_var.set(info_text)

    def update_gui_options(self):
        """根据版本更新GUI选项的可用性"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]

            # 更新GUI选项状态
            if version_info["supports_nogui"]:
                self.gui_radio_nogui.config(state="normal")
            else:
                self.gui_radio_nogui.config(state="disabled")
                if self.gui_mode_var.get() == "--nogui":
                    self.gui_mode_var.set("")

            if version_info["supports_gui"]:
                self.gui_radio_gui.config(state="normal")
            else:
                self.gui_radio_gui.config(state="disabled")
                if self.gui_mode_var.get() == "":
                    self.gui_mode_var.set("--nogui")

            # 如果当前GUI模式为空或不合适，设置为推荐模式
            current_mode = self.gui_mode_var.get()
            recommended_mode = "--nogui" if version_info.get("recommended_gui", "nogui") == "nogui" else ""

            if not current_mode or (current_mode == "--nogui" and not version_info["supports_nogui"]) or (current_mode == "" and not version_info["supports_gui"]):
                self.gui_mode_var.set(recommended_mode)

    def update_jvm_args(self):
        """根据版本更新推荐的JVM参数"""
        version_key = self.version_var.get()
        if version_key in self.minecraft_versions:
            version_info = self.minecraft_versions[version_key]
            current_args = self.jvm_args_var.get().strip()

            # 如果当前JVM参数为空，使用推荐参数
            if not current_args and version_info["default_jvm_args"]:
                self.jvm_args_var.set(version_info["default_jvm_args"])

    def search_java_versions(self):
        """搜索系统中可用的Java版本"""
        print("开始搜索Java安装...")
        java_installations = self.find_java_installations()

        if java_installations:
            # 格式化显示选项
            display_options = []
            self.java_paths = {}  # 存储显示文本到路径的映射

            for java_info in java_installations:
                display_text = f"{java_info['version']} - {java_info['path']}"
                display_options.append(display_text)
                self.java_paths[display_text] = java_info['path']
                print(f"找到Java: {display_text}")

            self.java_combo['values'] = display_options

            # 如果当前路径在列表中，选中它
            current_path = self.java_path_var.get()
            for display_text, path in self.java_paths.items():
                if path == current_path:
                    self.java_versions_var.set(display_text)
                    break

            print(f"总共找到 {len(java_installations)} 个Java安装")
        else:
            self.java_combo['values'] = ["未找到Java安装 - 点击搜索重试"]
            print("未找到任何Java安装")

    def find_java_installations(self):
        """查找系统中的Java安装"""
        java_installations = []

        try:
            # 检查PATH中的java
            try:
                result = subprocess.run(['java', '-version'],
                                      capture_output=True, text=True,
                                      stderr=subprocess.STDOUT, timeout=10)
                if result.returncode == 0:
                    version = self.parse_java_version(result.stdout)
                    java_installations.append({
                        'path': 'java',
                        'version': f"系统PATH中的Java {version}",
                        'type': 'system'
                    })
            except:
                pass

            # 根据操作系统搜索常见安装位置
            system = platform.system().lower()

            if system == 'windows':
                java_installations.extend(self.find_windows_java())
            elif system == 'linux':
                java_installations.extend(self.find_linux_java())
            elif system == 'darwin':  # macOS
                java_installations.extend(self.find_macos_java())

        except Exception as e:
            print(f"搜索Java时出错: {e}")

        return java_installations

    def find_windows_java(self):
        """在Windows系统中查找Java"""
        java_installations = []

        # 常见的Java安装路径
        search_paths = [
            "C:/Program Files/Java",
            "C:/Program Files (x86)/Java",
            "C:/Program Files/Eclipse Adoptium",
            "C:/Program Files/Eclipse Foundation",
            "C:/Program Files/Amazon Corretto",
            "C:/Program Files/Zulu",
            "C:/Program Files/Microsoft",  # Microsoft OpenJDK
            "C:/Program Files/AdoptOpenJDK",
            "C:/Program Files/OpenJDK",
            "C:/Program Files/BellSoft",  # Liberica JDK
            "C:/Program Files/Azul",      # Azul Zulu
            os.path.expanduser("~/AppData/Local/Programs/Java"),
            os.path.expanduser("~/AppData/Local/Microsoft/WindowsApps"),  # Microsoft Store Java
            "D:/Program Files/Java",     # 其他盘符
            "E:/Program Files/Java",
        ]

        print("搜索Windows Java安装路径...")
        for base_path in search_paths:
            print(f"检查路径: {base_path}")
            if os.path.exists(base_path):
                try:
                    items = os.listdir(base_path)
                    print(f"  找到子目录: {items}")
                    for item in items:
                        java_dir = os.path.join(base_path, item)
                        if os.path.isdir(java_dir):
                            # 检查多个可能的java.exe位置
                            possible_java_paths = [
                                os.path.join(java_dir, "bin", "java.exe"),
                                os.path.join(java_dir, "java.exe"),
                                os.path.join(java_dir, "jre", "bin", "java.exe"),
                            ]

                            for java_exe in possible_java_paths:
                                if os.path.exists(java_exe):
                                    print(f"  找到Java可执行文件: {java_exe}")
                                    version = self.get_java_version(java_exe)
                                    if version:
                                        java_installations.append({
                                            'path': java_exe,
                                            'version': f"Java {version} ({item})",
                                            'type': 'installed'
                                        })
                                        print(f"  版本: Java {version}")
                                    break
                except Exception as e:
                    print(f"  搜索 {base_path} 时出错: {e}")
                    continue
            else:
                print(f"  路径不存在: {base_path}")

        # 尝试通过注册表查找Java（Windows特有）
        try:
            java_installations.extend(self.find_java_from_registry())
        except Exception as e:
            print(f"注册表搜索失败: {e}")

        return java_installations

    def find_java_from_registry(self):
        """通过Windows注册表查找Java"""
        java_installations = []

        try:
            import winreg

            # 搜索注册表中的Java安装信息
            registry_paths = [
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\JavaSoft\Java Runtime Environment"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\JavaSoft\Java Development Kit"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\JavaSoft\JDK"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Eclipse Adoptium\JDK"),
                (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Eclipse Foundation\JDK"),
            ]

            for hkey, subkey in registry_paths:
                try:
                    with winreg.OpenKey(hkey, subkey) as key:
                        i = 0
                        while True:
                            try:
                                version_key = winreg.EnumKey(key, i)
                                with winreg.OpenKey(key, version_key) as version_reg:
                                    try:
                                        java_home, _ = winreg.QueryValueEx(version_reg, "JavaHome")
                                        java_exe = os.path.join(java_home, "bin", "java.exe")
                                        if os.path.exists(java_exe):
                                            java_installations.append({
                                                'path': java_exe,
                                                'version': f"Java {version_key} (注册表)",
                                                'type': 'registry'
                                            })
                                            print(f"从注册表找到: Java {version_key} at {java_exe}")
                                    except FileNotFoundError:
                                        pass
                                i += 1
                            except OSError:
                                break
                except FileNotFoundError:
                    continue

        except ImportError:
            print("winreg模块不可用，跳过注册表搜索")
        except Exception as e:
            print(f"注册表搜索出错: {e}")

        return java_installations

    def find_linux_java(self):
        """在Linux系统中查找Java"""
        java_installations = []

        # 常见的Java安装路径
        search_paths = [
            "/usr/lib/jvm",
            "/usr/java",
            "/opt/java",
            "/opt/jdk",
            "/usr/local/java",
            os.path.expanduser("~/java"),
        ]

        for base_path in search_paths:
            if os.path.exists(base_path):
                try:
                    for item in os.listdir(base_path):
                        java_dir = os.path.join(base_path, item)
                        if os.path.isdir(java_dir):
                            java_exe = os.path.join(java_dir, "bin", "java")
                            if os.path.exists(java_exe):
                                version = self.get_java_version(java_exe)
                                if version:
                                    java_installations.append({
                                        'path': java_exe,
                                        'version': f"Java {version} ({item})",
                                        'type': 'installed'
                                    })
                except:
                    continue

        return java_installations

    def find_macos_java(self):
        """在macOS系统中查找Java"""
        java_installations = []

        # macOS特有的Java路径
        search_paths = [
            "/Library/Java/JavaVirtualMachines",
            "/System/Library/Java/JavaVirtualMachines",
            os.path.expanduser("~/Library/Java/JavaVirtualMachines"),
        ]

        for base_path in search_paths:
            if os.path.exists(base_path):
                try:
                    for item in os.listdir(base_path):
                        java_dir = os.path.join(base_path, item, "Contents", "Home")
                        if os.path.isdir(java_dir):
                            java_exe = os.path.join(java_dir, "bin", "java")
                            if os.path.exists(java_exe):
                                version = self.get_java_version(java_exe)
                                if version:
                                    java_installations.append({
                                        'path': java_exe,
                                        'version': f"Java {version} ({item})",
                                        'type': 'installed'
                                    })
                except:
                    continue

        return java_installations

    def get_java_version(self, java_path):
        """获取指定Java可执行文件的版本"""
        try:
            # Windows需要特殊处理
            if os.name == 'nt':
                result = subprocess.run([java_path, '-version'],
                                      capture_output=True, text=True,
                                      timeout=10, creationflags=subprocess.CREATE_NO_WINDOW)
            else:
                result = subprocess.run([java_path, '-version'],
                                      capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                # Java版本信息通常在stderr中
                version_output = result.stderr if result.stderr else result.stdout
                return self.parse_java_version(version_output)
        except Exception as e:
            print(f"获取Java版本失败 {java_path}: {e}")
        return None

    def parse_java_version(self, version_output):
        """解析Java版本输出"""
        try:
            # 匹配版本号模式
            patterns = [
                r'version "(\d+\.\d+\.\d+[^"]*)"',  # Java 8及以下
                r'version "(\d+[^"]*)"',            # Java 9+
                r'openjdk version "([^"]*)"',       # OpenJDK
            ]

            for pattern in patterns:
                match = re.search(pattern, version_output)
                if match:
                    version = match.group(1)
                    # 简化版本显示
                    if version.startswith('1.8'):
                        return "8"
                    elif version.startswith('1.'):
                        return version[2:]
                    else:
                        return version.split('.')[0]
        except:
            pass
        return "未知版本"

    def search_java(self):
        """手动触发Java搜索"""
        self.search_java_versions()
        messagebox.showinfo("搜索完成", f"找到 {len(self.java_combo['values'])} 个Java安装")

    def on_java_selected(self, event=None):
        """Java版本选择回调"""
        selected = self.java_versions_var.get()
        if selected and selected in self.java_paths:
            self.java_path_var.set(self.java_paths[selected])
            self.update_preview()


def main():
    """主函数"""
    if HAS_DND:
        # 如果有拖放支持，使用TkinterDnD
        root = TkinterDnD.Tk()
    else:
        root = tk.Tk()

    MinecraftBatEditor(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("minecraft.ico")
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()


if __name__ == "__main__":
    main()
