#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的拖放测试脚本
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os

class SimpleDragTest:
    def __init__(self, root):
        self.root = root
        self.root.title("拖放测试")
        self.root.geometry("500x300")
        
        # 创建界面
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(main_frame, text="拖放测试", font=("Arial", 16, "bold")).pack(pady=10)
        
        # 说明
        ttk.Label(main_frame, text="测试不同的拖放方法:", font=("Arial", 10)).pack(pady=5)
        
        # 方法1: 右键提示
        ttk.Label(main_frame, text="方法1: 右键点击输入框查看拖放提示").pack(pady=5)
        self.entry1 = ttk.Entry(main_frame, width=50)
        self.entry1.pack(pady=5)
        self.entry1.bind('<Button-3>', self.show_drag_tip)
        
        # 方法2: 双击触发文件选择
        ttk.Label(main_frame, text="方法2: 双击输入框选择文件").pack(pady=5)
        self.entry2 = ttk.Entry(main_frame, width=50)
        self.entry2.pack(pady=5)
        self.entry2.bind('<Double-Button-1>', self.select_file)
        
        # 方法3: 浏览按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)
        
        ttk.Label(button_frame, text="方法3: 使用浏览按钮").pack(side=tk.LEFT, padx=5)
        self.entry3 = ttk.Entry(button_frame, width=40)
        self.entry3.pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="浏览", command=self.browse_file).pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        ttk.Label(main_frame, text="选择的文件:").pack(pady=(20, 5))
        self.result_text = tk.Text(main_frame, height=5, width=60)
        self.result_text.pack(pady=5)
        
        # 清除按钮
        ttk.Button(main_frame, text="清除", command=self.clear_all).pack(pady=10)
        
    def show_drag_tip(self, event):
        """显示拖放提示"""
        tip = """拖放文件的方法:
        
1. 复制文件路径到剪贴板，然后粘贴到输入框
2. 使用双击方法选择文件
3. 使用浏览按钮选择文件

注意: 真正的拖放功能需要安装 tkinterdnd2:
pip install tkinterdnd2"""
        
        messagebox.showinfo("拖放提示", tip)
    
    def select_file(self, event):
        """双击选择文件"""
        from tkinter import filedialog
        
        filename = filedialog.askopenfilename(
            title="选择文件",
            filetypes=[
                ("Java文件", "*.exe"),
                ("JAR文件", "*.jar"),
                ("所有文件", "*.*")
            ]
        )
        
        if filename:
            event.widget.delete(0, tk.END)
            event.widget.insert(0, filename)
            self.update_result(filename)
    
    def browse_file(self):
        """浏览选择文件"""
        from tkinter import filedialog
        
        filename = filedialog.askopenfilename(
            title="选择文件",
            filetypes=[
                ("Java文件", "*.exe"),
                ("JAR文件", "*.jar"),
                ("所有文件", "*.*")
            ]
        )
        
        if filename:
            self.entry3.delete(0, tk.END)
            self.entry3.insert(0, filename)
            self.update_result(filename)
    
    def update_result(self, filename):
        """更新结果显示"""
        self.result_text.delete(1.0, tk.END)
        
        info = f"文件路径: {filename}\n"
        info += f"文件名: {os.path.basename(filename)}\n"
        info += f"文件大小: {os.path.getsize(filename) if os.path.exists(filename) else '未知'} 字节\n"
        
        if filename.lower().endswith('.jar'):
            info += "文件类型: Minecraft JAR文件\n"
        elif filename.lower().endswith('.exe'):
            info += "文件类型: 可执行文件\n"
        else:
            info += "文件类型: 其他文件\n"
        
        self.result_text.insert(1.0, info)
    
    def clear_all(self):
        """清除所有内容"""
        self.entry1.delete(0, tk.END)
        self.entry2.delete(0, tk.END)
        self.entry3.delete(0, tk.END)
        self.result_text.delete(1.0, tk.END)

def main():
    root = tk.Tk()
    app = SimpleDragTest(root)
    root.mainloop()

if __name__ == "__main__":
    main()
