#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java搜索功能测试脚本
"""

import os
import subprocess
import platform
import re

def test_java_search():
    """测试Java搜索功能"""
    print("=== Java搜索测试 ===")
    print(f"操作系统: {platform.system()}")
    print()
    
    # 测试PATH中的Java
    print("1. 测试PATH中的Java:")
    try:
        result = subprocess.run(['java', '-version'], 
                              capture_output=True, text=True, 
                              stderr=subprocess.STDOUT, timeout=10)
        if result.returncode == 0:
            version = parse_java_version(result.stdout)
            print(f"   ✓ 找到系统Java: {version}")
            print(f"   输出: {result.stdout.strip()}")
        else:
            print("   ✗ 系统PATH中没有Java")
    except Exception as e:
        print(f"   ✗ 检查系统Java失败: {e}")
    
    print()
    
    # 测试常见安装路径
    print("2. 搜索常见安装路径:")
    if platform.system().lower() == 'windows':
        search_windows_java()
    elif platform.system().lower() == 'linux':
        search_linux_java()
    elif platform.system().lower() == 'darwin':
        search_macos_java()

def parse_java_version(version_output):
    """解析Java版本输出"""
    try:
        patterns = [
            r'version "(\d+\.\d+\.\d+[^"]*)"',  # Java 8及以下
            r'version "(\d+[^"]*)"',            # Java 9+
            r'openjdk version "([^"]*)"',       # OpenJDK
        ]
        
        for pattern in patterns:
            match = re.search(pattern, version_output)
            if match:
                version = match.group(1)
                if version.startswith('1.8'):
                    return "8"
                elif version.startswith('1.'):
                    return version[2:]
                else:
                    return version.split('.')[0]
    except:
        pass
    return "未知版本"

def search_windows_java():
    """搜索Windows Java安装"""
    search_paths = [
        "C:/Program Files/Java",
        "C:/Program Files (x86)/Java",
        "C:/Program Files/Eclipse Adoptium",
        "C:/Program Files/Amazon Corretto",
        "C:/Program Files/Zulu",
        "C:/Program Files/Microsoft",
        "C:/Program Files/OpenJDK",
    ]
    
    found_count = 0
    for base_path in search_paths:
        print(f"   检查: {base_path}")
        if os.path.exists(base_path):
            try:
                items = os.listdir(base_path)
                for item in items:
                    java_dir = os.path.join(base_path, item)
                    if os.path.isdir(java_dir):
                        java_exe = os.path.join(java_dir, "bin", "java.exe")
                        if os.path.exists(java_exe):
                            version = get_java_version(java_exe)
                            if version:
                                print(f"     ✓ 找到: Java {version} - {java_exe}")
                                found_count += 1
            except Exception as e:
                print(f"     ✗ 搜索失败: {e}")
        else:
            print(f"     - 路径不存在")
    
    if found_count == 0:
        print("   ✗ 未找到任何Java安装")
    else:
        print(f"   总共找到 {found_count} 个Java安装")

def search_linux_java():
    """搜索Linux Java安装"""
    search_paths = [
        "/usr/lib/jvm",
        "/usr/java",
        "/opt/java",
        "/opt/jdk",
        "/usr/local/java",
    ]
    
    found_count = 0
    for base_path in search_paths:
        print(f"   检查: {base_path}")
        if os.path.exists(base_path):
            try:
                items = os.listdir(base_path)
                for item in items:
                    java_dir = os.path.join(base_path, item)
                    if os.path.isdir(java_dir):
                        java_exe = os.path.join(java_dir, "bin", "java")
                        if os.path.exists(java_exe):
                            version = get_java_version(java_exe)
                            if version:
                                print(f"     ✓ 找到: Java {version} - {java_exe}")
                                found_count += 1
            except Exception as e:
                print(f"     ✗ 搜索失败: {e}")
        else:
            print(f"     - 路径不存在")
    
    if found_count == 0:
        print("   ✗ 未找到任何Java安装")
    else:
        print(f"   总共找到 {found_count} 个Java安装")

def search_macos_java():
    """搜索macOS Java安装"""
    search_paths = [
        "/Library/Java/JavaVirtualMachines",
        "/System/Library/Java/JavaVirtualMachines",
    ]
    
    found_count = 0
    for base_path in search_paths:
        print(f"   检查: {base_path}")
        if os.path.exists(base_path):
            try:
                items = os.listdir(base_path)
                for item in items:
                    java_dir = os.path.join(base_path, item, "Contents", "Home")
                    if os.path.isdir(java_dir):
                        java_exe = os.path.join(java_dir, "bin", "java")
                        if os.path.exists(java_exe):
                            version = get_java_version(java_exe)
                            if version:
                                print(f"     ✓ 找到: Java {version} - {java_exe}")
                                found_count += 1
            except Exception as e:
                print(f"     ✗ 搜索失败: {e}")
        else:
            print(f"     - 路径不存在")
    
    if found_count == 0:
        print("   ✗ 未找到任何Java安装")
    else:
        print(f"   总共找到 {found_count} 个Java安装")

def get_java_version(java_path):
    """获取Java版本"""
    try:
        result = subprocess.run([java_path, '-version'], 
                              capture_output=True, text=True, 
                              stderr=subprocess.STDOUT, timeout=10)
        if result.returncode == 0:
            return parse_java_version(result.stdout)
    except:
        pass
    return None

if __name__ == "__main__":
    test_java_search()
    print("\n测试完成！")
    input("按回车键退出...")
